import pg from 'pg';
const { Client } = pg;

// Get environment variables
const dbPassword = 'wlGnMGoz9xFFASUz';

async function applyMigration() {
  try {
    // Direct database connection string
    const connectionString = `postgresql://postgres.qnfmiotatmkoumlymynq:${dbPassword}@aws-0-eu-central-1.pooler.supabase.com:5432/postgres`;
    
    // Use pg library for direct database access
    const client = new Client({
      connectionString: connectionString,
      ssl: { rejectUnauthorized: false }
    });

    await client.connect();
    console.log('Connected to database');

    // First check existing constraints
    const checkResult = await client.query(`
      SELECT conname FROM pg_constraint 
      WHERE conrelid = 'model_library'::regclass 
      AND contype = 'u'
    `);
    
    console.log('Current unique constraints:', checkResult.rows);

    // Execute the migration
    console.log('Applying temporary fix...');
    
    // Drop old constraint - this will allow creating models with duplicate codes temporarily
    await client.query('ALTER TABLE public.model_library DROP CONSTRAINT IF EXISTS model_library_code_key');
    console.log('Dropped old constraint that was blocking model creation');
    
    console.log('\nIMPORTANT: This is a temporary fix!');
    console.log('The full migration chain needs to be applied to add proper scoping support.');
    console.log('Missing migrations that need to be applied:');
    console.log('- 20250722000000_create_model_library_tables.sql');
    console.log('- 20250723000000_add_model_prompt_fields.sql'); 
    console.log('- 20250724000000_add_model_scoping.sql (adds scope_type column)');
    console.log('- 20250726064917_fix_model_library_unique_constraint.sql (adds new constraint)');

    // Verify the new constraint
    const verifyResult = await client.query(`
      SELECT conname FROM pg_constraint 
      WHERE conrelid = 'model_library'::regclass 
      AND contype = 'u'
    `);
    
    console.log('New unique constraints:', verifyResult.rows);

    await client.end();
    console.log('Migration completed successfully!');

  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

applyMigration();