#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Use local Supabase instance
const supabaseUrl = 'http://localhost:54321';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testModelConstraint() {
  console.log('Testing model_library unique constraint...\n');

  try {
    // 1. Check existing constraints
    const { data: constraints, error: constraintError } = await supabase
      .rpc('get_model_constraints');
    
    if (constraintError) {
      console.log('Creating helper function...');
      // Create a helper function to check constraints
      await supabase.rpc('execute_sql', {
        query: `
          CREATE OR REPLACE FUNCTION get_model_constraints()
          RETURNS TABLE(constraint_name text, constraint_type text, definition text)
          LANGUAGE sql
          SECURITY DEFINER
          AS $$
            SELECT 
              conname::text as constraint_name,
              contype::text as constraint_type,
              pg_get_constraintdef(oid)::text as definition
            FROM pg_constraint 
            WHERE conrelid = 'public.model_library'::regclass 
            ORDER BY conname;
          $$;
        `
      });
    }

    // 2. List current models to verify constraint is working
    console.log('Checking model_library table...');

    // 3. Test inserting models with same code but different scopes
    console.log('\nTesting model insertions:');
    
    // First, let's check what's already in the table
    const { data: existingModels } = await supabase
      .from('model_library')
      .select('*')
      .order('code');
    
    console.log('Existing models:', existingModels?.length || 0);
    existingModels?.forEach(m => {
      console.log(`  - ${m.code} (${m.scope_type}): ${m.name}`);
    });

    // Test scenarios
    console.log('\nTest scenarios:');
    console.log('1. Two global models with same code: Should FAIL');
    console.log('2. Global and org model with same code: Should SUCCEED');
    console.log('3. Two org models in different orgs with same code: Should SUCCEED');
    console.log('4. Two org models in same org with same code: Should FAIL');

    console.log('\nConstraint test completed!');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testModelConstraint();