-- Fix model_library unique constraint to allow same code for different scopes
-- This allows organizations and collections to have their own models with the same codes (e.g., S, M, L, XL)

-- First, drop the existing global unique constraint on code
ALTER TABLE public.model_library 
DROP CONSTRAINT IF EXISTS model_library_code_key;

-- Add a new compound unique constraint that considers scope
-- This ensures uniqueness within each scope context:
-- - Global models must have unique codes among global models
-- - Organization models must have unique codes within the same organization
-- - Collection models must have unique codes within the same collection
ALTER TABLE public.model_library 
ADD CONSTRAINT model_library_unique_code_per_scope 
UNIQUE NULLS NOT DISTINCT (
  code,
  scope_type,
  organization_id,
  collection_id
);

-- Add comment explaining the constraint
COMMENT ON CONSTRAINT model_library_unique_code_per_scope ON public.model_library IS 
'Ensures model codes are unique within their scope context. Global models have unique codes globally, organization models have unique codes per organization, and collection models have unique codes per collection.';