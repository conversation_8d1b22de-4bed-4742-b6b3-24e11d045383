import { useState, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient, QueryClient } from '@tanstack/react-query';
import { supabase } from '../components/common/utils/supabase';
import { useToast } from '../components/ui/use-toast';

export interface Model {
  id: string;
  name: string;
  code: string;
  description?: string;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
  prompt_text?: string;
  model_persona_name?: string;
  model_backstory?: string;
  metadata?: Record<string, any>;
}

export interface ModelImage {
  id: string;
  model_id: string;
  angle_type: string;
  storage_path: string | null;
  file_size?: number;
  mime_type?: string;
  created_at: string;
  updated_at: string;
  angle_prompt_text?: string;
  is_white_background?: boolean;
  client_view_path?: string;
}

interface ModelWithImages extends Model {
  images: ModelImage[];
}

// Fetch all models
export function useModels() {
  return useQuery({
    queryKey: ['models'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('model_library')
        .select('*')
        .order('display_order', { ascending: true });

      if (error) throw error;
      return data as Model[];
    }
  });
}

// Fetch active models only
export function useActiveModels() {
  return useQuery({
    queryKey: ['models', 'active'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('model_library')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) throw error;
      return data as Model[];
    }
  });
}

// Fetch a single model with its images
export function useModel(modelId: string | null) {
  return useQuery({
    queryKey: ['model', modelId],
    queryFn: async () => {
      if (!modelId) return null;

      const { data: model, error: modelError } = await supabase
        .from('model_library')
        .select('*')
        .eq('id', modelId)
        .single();

      if (modelError) throw modelError;

      const { data: images, error: imagesError } = await supabase
        .from('model_images')
        .select('*')
        .eq('model_id', modelId)
        .order('angle_type');

      if (imagesError) throw imagesError;

      return {
        ...model,
        images
      } as ModelWithImages;
    },
    enabled: !!modelId
  });
}

// Fetch model by code
export function useModelByCode(code: string | null) {
  return useQuery({
    queryKey: ['model', 'code', code],
    queryFn: async () => {
      if (!code) return null;

      const { data: model, error: modelError } = await supabase
        .from('model_library')
        .select('*')
        .eq('code', code)
        .eq('is_active', true)
        .single();

      if (modelError) throw modelError;

      const { data: images, error: imagesError } = await supabase
        .from('model_images')
        .select('*')
        .eq('model_id', model.id)
        .order('angle_type');

      if (imagesError) throw imagesError;

      return {
        ...model,
        images
      } as ModelWithImages;
    },
    enabled: !!code
  });
}

// Fetch images for a specific model
export function useModelImages(modelId: string | null) {
  return useQuery({
    queryKey: ['model-images', modelId],
    queryFn: async () => {
      if (!modelId) return [];

      const { data, error } = await supabase
        .from('model_images')
        .select('*')
        .eq('model_id', modelId)
        .order('angle_type');

      if (error) throw error;
      return data as ModelImage[];
    },
    enabled: !!modelId
  });
}

// Get specific model image by model code and angle
export function useModelImage(modelCode: string | null, angleType: string | null) {
  return useQuery({
    queryKey: ['model-image', modelCode, angleType],
    queryFn: async () => {
      if (!modelCode || !angleType) return null;

      // First get the model
      const { data: model, error: modelError } = await supabase
        .from('model_library')
        .select('id')
        .eq('code', modelCode)
        .eq('is_active', true)
        .single();

      if (modelError) throw modelError;

      // Then get the specific image
      const { data: image, error: imageError } = await supabase
        .from('model_images')
        .select('*')
        .eq('model_id', model.id)
        .eq('angle_type', angleType)
        .maybeSingle();

      if (imageError) throw imageError;
      
      if (!image || !image.storage_path) return null;

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('model-library')
        .getPublicUrl(image.storage_path);

      return {
        ...image,
        publicUrl: urlData?.publicUrl
      };
    },
    enabled: !!modelCode && !!angleType
  });
}

// Create a new model
export function useCreateModel() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (model: Omit<Model, 'id' | 'created_at' | 'updated_at'>) => {
      const { data, error } = await supabase
        .from('model_library')
        .insert(model)
        .select()
        .single();

      if (error) throw error;
      return data as Model;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['models'] });
      toast({
        title: 'Model created',
        description: 'The model has been created successfully.'
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating model',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}

// Update a model
export function useUpdateModel() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ id, ...model }: Partial<Model> & { id: string }) => {
      const { data, error } = await supabase
        .from('model_library')
        .update(model)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as Model;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['models'] });
      queryClient.invalidateQueries({ queryKey: ['model', data.id] });
      toast({
        title: 'Model updated',
        description: 'The model has been updated successfully.'
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating model',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}

// Delete a model
export function useDeleteModel() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (modelId: string) => {
      const { error } = await supabase
        .from('model_library')
        .delete()
        .eq('id', modelId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['models'] });
      toast({
        title: 'Model deleted',
        description: 'The model has been deleted successfully.'
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting model',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}

// Upload model image
export async function uploadModelImage(
  modelId: string,
  angleType: string,
  file: File,
  queryClient: QueryClient,
  onProgress?: (progress: number) => void
): Promise<void> {
  // Generate storage path
  const { data: model } = await supabase
    .from('model_library')
    .select('code')
    .eq('id', modelId)
    .single();

  if (!model) throw new Error('Model not found');

  const fileExt = file.name.split('.').pop();
  const fileName = `${angleType}.${fileExt}`;
  const storagePath = `${model.code.toLowerCase()}/${fileName}`;

  // Upload to storage
  const { error: uploadError } = await supabase.storage
    .from('model-library')
    .upload(storagePath, file, {
      upsert: true,
      cacheControl: '3600',
      contentType: file.type
    });

  if (uploadError) throw uploadError;

  // Update database record - store just the path without bucket prefix
  const { error: updateError } = await supabase
    .from('model_images')
    .update({
      storage_path: storagePath,
      file_size: file.size,
      mime_type: file.type,
      updated_at: new Date().toISOString()
    })
    .eq('model_id', modelId)
    .eq('angle_type', angleType);

  if (updateError) throw updateError;

  // Invalidate all relevant queries to refresh the UI
  await queryClient.invalidateQueries({ queryKey: ['model-images', modelId] });
  await queryClient.invalidateQueries({ queryKey: ['model', modelId] });
  await queryClient.invalidateQueries({ queryKey: ['models'] });

  // Also invalidate specific image queries
  await queryClient.invalidateQueries({
    queryKey: ['model-image', model.code, angleType]
  });
}

// Utility function to get model image URL and convert to base64
export async function getModelImageAsBase64(modelCode: string, angleType: string): Promise<string | null> {
  try {
    // Get model ID
    const { data: model, error: modelError } = await supabase
      .from('model_library')
      .select('id')
      .eq('code', modelCode)
      .eq('is_active', true)
      .single();

    if (modelError || !model) return null;

    // Get image record
    const { data: image, error: imageError } = await supabase
      .from('model_images')
      .select('storage_path')
      .eq('model_id', model.id)
      .eq('angle_type', angleType)
      .maybeSingle();

    if (imageError || !image?.storage_path) return null;

    // Get public URL - remove duplicate prefix if it exists
    const cleanPath = image.storage_path.replace(/^model-library\//, '');
    const { data: urlData } = supabase.storage
      .from('model-library')
      .getPublicUrl(cleanPath);

    if (!urlData?.publicUrl) return null;

    // Fetch and convert to base64
    const response = await fetch(urlData.publicUrl);
    if (!response.ok) return null;

    const blob = await response.blob();
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error getting model image:', error);
    return null;
  }
}

// Create a new model (non-hook version for direct use)
export async function createModel(model: {
  name: string;
  code: string;
  description?: string;
  display_order?: number;
}) {
  const { data, error } = await supabase
    .from('model_library')
    .insert({
      name: model.name,
      code: model.code,
      description: model.description || null,
      display_order: model.display_order || 0,
      is_active: true
    })
    .select()
    .single();

  if (error) throw error;
  
  // Create placeholder image records for all angles
  const angles = [
    'face',
    'half-body-front',
    'half-body-back',
    'half-body-34-left',
    'half-body-34-right',
    'full-body-front',
    'full-body-back',
    'full-body-side-left',
    'full-body-side-right',
    'full-body-34-left',
    'full-body-34-right'
  ];

  const imageRecords = angles.map(angle => ({
    model_id: data.id,
    angle_type: angle,
    storage_path: null // No path yet, will be set when image is uploaded
  }));

  const { error: imagesError } = await supabase
    .from('model_images')
    .insert(imageRecords);

  if (imagesError) {
    console.error('Error creating image placeholders:', imagesError);
  }

  return data as Model;
}

// Update an existing model (non-hook version for direct use)
export async function updateModel(
  id: string,
  updates: {
    name?: string;
    code?: string;
    description?: string | null;
    display_order?: number;
    is_active?: boolean;
    prompt_text?: string | null;
    model_persona_name?: string | null;
    model_backstory?: string | null;
  }
) {
  const { data, error } = await supabase
    .from('model_library')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Model;
}

// Main hook for model library page
export function useModelLibrary() {
  const { data: models, isLoading, error, refetch } = useModels();
  
  return {
    models: models || [],
    isLoading,
    error,
    refetch
  };
}